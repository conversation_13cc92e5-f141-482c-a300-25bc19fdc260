<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>drqp_control</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <depend>hardware_interface</depend>
  <depend>pluginlib</depend>
  <depend>ros2_control</depend>
  <depend>ros2_controllers</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>drqp_a1_16_driver</depend>
  <depend>drqp_description</depend>
  <depend>ament_index_cpp</depend>
  <depend>yaml-cpp</depend>
  <exec_depend>joy</exec_depend>
  <exec_depend>ros2_controllers_test_nodes</exec_depend>
  <exec_depend>rqt_joint_trajectory_controller</exec_depend>


  <test_depend>ament_lint_auto</test_depend>
  <test_depend>drqp_lint_common</test_depend>
  <test_depend>ament_cmake_gmock</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
