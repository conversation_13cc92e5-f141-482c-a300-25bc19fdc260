publisher_joint_trajectory_controller:
  ros__parameters:

    controller_name: "joint_trajectory_controller"
    wait_sec_between_publish: 4

    goal_names: ["pos0", "pos1", "pos2", "pos3", "pos4", "pos5", "pos6"]
    pos0:
      positions: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
      effort: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    pos1:
      # positions: [0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785, 0.785]
      positions: [3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0]
      effort: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    pos2:
      # positions: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
      positions: [-3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0, -3.0]
      effort: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    pos3:
      positions: [-0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785, -0.785]
      effort: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    pos4:
      positions: [3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0]
      effort: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    pos5:
      positions: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
      effort: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    pos6:
      positions: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
      effort: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

    joints:
      - dr_qp/left_front_coxa
      - dr_qp/left_front_femur
      - dr_qp/left_front_tibia

      - dr_qp/right_front_coxa
      - dr_qp/right_front_femur
      - dr_qp/right_front_tibia

      - dr_qp/left_middle_coxa
      - dr_qp/left_middle_femur
      - dr_qp/left_middle_tibia

      - dr_qp/right_middle_coxa
      - dr_qp/right_middle_femur
      - dr_qp/right_middle_tibia

      - dr_qp/left_back_coxa
      - dr_qp/left_back_femur
      - dr_qp/left_back_tibia

      - dr_qp/right_back_coxa
      - dr_qp/right_back_femur
      - dr_qp/right_back_tibia

    check_starting_point: false
    starting_point_limits:
      dr_qp/left_front_coxa: [-0.1, 0.1]
      dr_qp/left_front_femur: [-0.1, 0.1]
      dr_qp/left_front_tibia: [-0.1, 0.1]
