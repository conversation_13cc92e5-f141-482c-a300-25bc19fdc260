<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>drqp_brain</name>
  <version>0.1.0</version>
  <description>IK solvers and other high level control algorithms</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <exec_depend>drqp_interfaces</exec_depend>
  <exec_depend>drqp_control</exec_depend>
  <exec_depend>rclpy</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>launch</exec_depend>
  <exec_depend>launch_ros</exec_depend>

  <exec_depend>python3-numpy</exec_depend>
  <exec_depend>python3-scipy</exec_depend>

  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <export>
    <build_type>ament_python</build_type>
  </export>
</package>
