# Copyright (c) 2017-2025 <PERSON>
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.

import enum

from drqp_brain.geometry import AffineTransform, Line3D, Point3D
import numpy as np


class HexapodLeg(enum.Enum):
    left_front = enum.auto()
    left_middle = enum.auto()
    left_back = enum.auto()
    right_front = enum.auto()
    right_middle = enum.auto()
    right_back = enum.auto()


def safe_arccos(num):
    if num < -1:
        return False, np.pi  # np.acos(-1)
    elif num > 1:
        return False, 0.0  # np.acos(1)
    else:
        return True, np.arccos(num)


class HexapodModel:
    def __init__(
        self,
        front_offset=100.0,  # x offset for the front and back legs
        side_offset=100.0,  # y offset fo the front and back legs
        middle_offset=100.0,  # x offset for the middle legs
        coxa_len=100.0,
        femur_len=100.0,
        tibia_len=100.0,
        body_transform=AffineTransform.identity(),
        leg_rotation=[0, 0, 45],
    ):
        leg_rotation = np.array(leg_rotation)

        self.left_front = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.left_front,
            rotation=leg_rotation,
            location_on_body=[front_offset, side_offset, 0.0],
        )
        self.left_middle = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.left_middle,
            rotation=leg_rotation * 2,
            location_on_body=[0.0, middle_offset, 0.0],
        )
        self.left_back = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.left_back,
            rotation=leg_rotation * 3,
            location_on_body=[-front_offset, side_offset, 0.0],
        )

        self.right_front = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.right_front,
            rotation=leg_rotation * -1,
            location_on_body=[front_offset, -side_offset, 0.0],
        )
        self.right_middle = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.right_middle,
            rotation=leg_rotation * -2,
            location_on_body=[0.0, -middle_offset, 0.0],
        )
        self.right_back = LegModel(
            coxa_len,
            femur_len,
            tibia_len,
            label=HexapodLeg.right_back,
            rotation=leg_rotation * -3,
            location_on_body=[-front_offset, -side_offset, 0.0],
        )
        self.__named_legs = {
            leg.label: leg
            for leg in [
                self.left_front,
                self.left_middle,
                self.left_back,
                self.right_front,
                self.right_middle,
                self.right_back,
            ]
        }

        # Head, x-forward
        self.__head_base_line = Line3D(Point3D([0, 0, 0]), Point3D([front_offset, 0, 0]), 'Head')

        # Setting body transform has to be last as it will update head and all the lgs
        self.body_transform = body_transform

    def forward_kinematics(self, alpha, beta, gamma):
        for leg in self.legs:
            leg.forward_kinematics(alpha, beta, gamma)

    def move_legs_to(self, foot_targets, verbose=False):
        results = []
        for leg, target in zip(self.legs, foot_targets):
            reached = leg.move_to(target, verbose)
            results.append(reached)
        return results

    @property
    def legs(self):
        return self.__named_legs.values()

    @property
    def named_legs(self):
        return self.__named_legs

    @property
    def body_transform(self):
        return self.left_front.body_transform

    @body_transform.setter
    def body_transform(self, body_transform):
        for leg in self.legs:
            leg.body_transform = body_transform

        self.head = body_transform.apply_line(self.__head_base_line)


class LegModel:
    """A leg model class."""

    def __init__(
        self,
        coxa_length: float,
        femur_length: float,
        tibia_length: float,
        label: HexapodLeg,
        location_on_body=[0, 0, 0],
        rotation=[0, 0, 0],
        body_transform=AffineTransform.identity(),
    ):
        self.coxa_length = coxa_length
        self.femur_length = femur_length
        self.tibia_length = tibia_length

        self.label = label

        self.location_on_body = location_on_body
        self.rotation = rotation
        self._body_transform = body_transform
        self.update_base_transforms()

        # Initialize leg to neutral position
        self.forward_kinematics(0, 0, 0)

    @property
    def body_transform(self):
        return self._body_transform

    @body_transform.setter
    def body_transform(self, value):
        self._body_transform = value
        self.update_base_transforms()

    def update_base_transforms(self):
        self.body_link = self.body_transform @ AffineTransform.from_translation(
            self.location_on_body
        )
        self.body_joint = self.body_link @ AffineTransform.from_rotvec(self.rotation, degrees=True)

    @property
    def lines(self):
        return [
            Line3D(self.body_start, self.body_end, 'Body'),
            Line3D(self.body_end, self.coxa_end, 'Coxa'),
            Line3D(self.coxa_end, self.femur_end, 'Femur'),
            Line3D(self.femur_end, self.tibia_end, 'Tibia'),
        ]

    @property
    def xy(self):
        return [line.xy for line in self.lines]

    @property
    def xz(self):
        return [line.xz for line in self.lines]

    @property
    def yz(self):
        return [line.yz for line in self.lines]

    def __iter__(self):
        return iter(self.lines)

    def __repr__(self):
        return f'LegModel(body_start={self.body_start},\
            body_end={self.body_end}, \
            coxa_end={self.coxa_end}, \
            femur_end={self.femur_end}, \
            tibia_end={self.tibia_end})'

    # Leg Forward kinematics - START
    def forward_kinematics(
        self,
        alpha,
        beta,
        gamma,
    ):
        self.update_base_transforms()

        self.coxa_angle = alpha
        self.femur_angle = beta
        self.tibia_angle = gamma

        self.coxa_joint = self.body_joint @ AffineTransform.from_rotvec(
            [0, 0, alpha],
            degrees=True,
        )
        self.coxa_link = self.coxa_joint @ AffineTransform.from_translation(
            [self.coxa_length, 0, 0]
        )

        self.femur_joint = self.coxa_link @ AffineTransform.from_rotvec(
            [0, beta, 0],
            degrees=True,
        )
        self.femur_link = self.femur_joint @ AffineTransform.from_translation(
            [self.femur_length, 0, 0]
        )

        self.tibia_joint = self.femur_link @ AffineTransform.from_rotvec(
            [0, gamma, 0],
            degrees=True,
        )
        self.tibia_link = self.tibia_joint @ AffineTransform.from_translation(
            [self.tibia_length, 0, 0]
        )

        # Calculate global positions using transformations
        identity_point = Point3D([0, 0, 0])

        self.body_start = self.body_transform.apply_point(identity_point)

        self.body_end = self.body_link.apply_point(identity_point)
        self.body_end.label = rf'$\alpha$={alpha}°'

        self.coxa_end = self.coxa_link.apply_point(identity_point)
        self.coxa_end.label = rf'$\beta$={beta}°'

        self.femur_end = self.femur_link.apply_point(identity_point)
        self.femur_end.label = rf'$\gamma$={gamma}°'

        self.tibia_end = self.tibia_link.apply_point(identity_point)
        self.tibia_end.label = 'Foot'
        # Leg Forward kinematics - END

    def move_to(self, foot_target: Point3D, verbose=False):
        reached_target, alpha, beta, gamma = self.inverse_kinematics(foot_target, verbose)
        if verbose:
            print(
                f'{self.label} moving to {foot_target}. \
                  new angles: {alpha=:.4f}\t{beta=:.4f}\t{gamma=:.4f}'
            )
        self.forward_kinematics(alpha, beta, gamma)
        return reached_target

    def inverse_kinematics(self, foot_target: Point3D, verbose=False):
        localized_foot_target = self.to_local(foot_target)
        alpha, X_tick = self._inverse_kinematics_xy(localized_foot_target)
        solvable, beta, gamma = self._inverse_kinematics_xz(
            localized_foot_target.z,
            X_tick,
            verbose=verbose,
        )
        return solvable, alpha, beta, gamma

    def to_local(self, point):
        return self.body_joint.inverse.apply_point(point)

    @staticmethod
    def _inverse_kinematics_xy(localized_foot_target: Point3D):
        alpha = np.degrees(np.arctan2(localized_foot_target.y, localized_foot_target.x))
        X_tick = np.hypot(localized_foot_target.x, localized_foot_target.y)
        return alpha, X_tick

    def _inverse_kinematics_xz(
        self,
        z_offset: float,
        X_tick: float,
        verbose=False,
    ):
        """
        XZ axis Inverse kinematics solver for 3DOF leg.

        Math as described above

        Parameters
        ----------
        z_offset:
          z offset from the coxa joint of the foot target in meters

        X_tick:
          X distance from the coxa joint of the foot target in meters,
          returned by the inverse_kinematics_xy

        verbose:
          print debug information

        """
        D = -z_offset
        T = X_tick - self.coxa_length
        L = np.hypot(D, T)

        solvable_theta1, theta1_rad = safe_arccos(
            (L**2 + self.femur_length**2 - self.tibia_length**2) / (2 * L * self.femur_length)
        )
        theta1 = np.degrees(theta1_rad)

        theta2 = np.degrees(np.arctan2(T, D))
        solvable_phi, phi_rad = safe_arccos(
            (self.tibia_length**2 + self.femur_length**2 - L**2)
            / (2 * self.tibia_length * self.femur_length)
        )
        phi = np.degrees(phi_rad)

        # The right hand coordinate system is used, so the angle offsets are inverted
        beta = 90 - (theta1 + theta2)
        gamma = 180 - phi
        if verbose:
            print(
                f'{theta1=} - solvable={solvable_theta1}\n\
                {theta2=}\n{phi=} - solvable={solvable_phi}\n\n\
                {beta=}\n\
                {gamma=}'
            )
        return solvable_theta1 and solvable_phi, beta, gamma
