<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>drqp_lint_common</name>
  <version>1.0.0</version>
  <description>A set of linters for Dr.QP project to be used instead of ament_lint_common</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <!-- Based on https://github.com/ament/ament_lint/blob/jazzy/ament_lint_common/package.xml -->
  <buildtool_depend>ament_cmake_core</buildtool_depend>
  <buildtool_depend>ament_cmake_export_dependencies</buildtool_depend>

  <buildtool_export_depend>ament_cmake_core</buildtool_export_depend>

  <exec_depend>ament_cmake_clang_format</exec_depend>
  <!--<exec_depend>ament_cmake_clang_tidy</exec_depend>-->
  <exec_depend>ament_cmake_copyright</exec_depend>
  <exec_depend>ament_cmake_cppcheck</exec_depend>
  <exec_depend>ament_cmake_cpplint</exec_depend>
  <exec_depend>ament_cmake_flake8</exec_depend>
  <exec_depend>ament_cmake_lint_cmake</exec_depend>
  <exec_depend>ament_cmake_pep257</exec_depend>
  <!-- <exec_depend>ament_cmake_uncrustify</exec_depend> Dr.QP: this check conflicts with clangd formatting -->
  <exec_depend>ament_cmake_xmllint</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
