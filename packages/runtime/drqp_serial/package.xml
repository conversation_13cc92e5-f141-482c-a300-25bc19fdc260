<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>drqp_serial</name>
  <version>1.0.0</version>
  <description>A collection of classes to work with UART ports on Unix and via TCP. Depends on boost</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>boost</depend>
  <depend>drqp_rapidjson</depend>

  <test_depend>catch_ros2</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>drqp_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
