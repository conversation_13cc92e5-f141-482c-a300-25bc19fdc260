<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
  <xacro:macro name="drqp_leg_ros2_control" params="robot_name leg_name coxa_servo_id servo_inverted:=False offset_coxa:=0.0 offset_femur:=0.0 offset_tibia:=0.0">
    <joint name="${robot_name}/${leg_name}_coxa">
      <command_interface name="position">
        <param name="servo_id">${coxa_servo_id}</param>
        <param name="inverted">False</param>
        <param name="min">${radians(-90)}</param>
        <param name="max">${radians(90)}</param>
        <param name="offset_rads">${offset_coxa}</param>
        <param name="initial_value">0.0</param> <!-- Initial value for mock_hardware -->
      </command_interface>
      <command_interface name="effort" />
      <state_interface name="velocity" />
      <state_interface name="position"/>
      <state_interface name="pwm"/>
    </joint>

    <joint name="${robot_name}/${leg_name}_femur">
      <command_interface name="position">
        <param name="servo_id">${coxa_servo_id + 2}</param>
        <param name="inverted">${servo_inverted}</param>
        <param name="min">${radians(-98)}</param>
        <param name="max">${radians(90)}</param>
        <param name="offset_rads">${offset_femur}</param>
        <param name="initial_value">0.0</param> <!-- Initial value for mock_hardware -->
      </command_interface>
      <command_interface name="effort" />
      <state_interface name="velocity" />
      <state_interface name="position"/>
      <state_interface name="pwm"/>
    </joint>

    <joint name="${robot_name}/${leg_name}_tibia">
      <command_interface name="position">
        <param name="servo_id">${coxa_servo_id + 4}</param>
        <param name="inverted">${servo_inverted}</param>
        <param name="min">${radians(-80)}</param>
        <param name="max">${radians(110)}</param>
        <param name="offset_rads">${offset_tibia}</param>
        <param name="initial_value">0.0</param> <!-- Initial value for mock_hardware -->
      </command_interface>
      <command_interface name="effort" />
      <state_interface name="velocity" />
      <state_interface name="position"/>
      <state_interface name="pwm"/>
    </joint>
  </xacro:macro>

  <xacro:macro name="drqp_ros2_control" params="robot_name device_address:=/dev/ttySC0">
    <ros2_control name="${robot_name}-servos" type="system" rw_rate="20" is_async="true" thread_priority="30">
      <hardware>
        <plugin>drqp_control/a1_16_hardware_interface</plugin>
        <param name="device_address">${device_address}</param>
        <param name="baud_rate">115200</param>
      </hardware>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="left_front" coxa_servo_id="1"/>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="right_front" coxa_servo_id="2" servo_inverted="True"/>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="left_middle" coxa_servo_id="13"/>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="right_middle" coxa_servo_id="14" servo_inverted="True"/>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="left_back" coxa_servo_id="7"/>
      <xacro:drqp_leg_ros2_control robot_name="${robot_name}" leg_name="right_back" coxa_servo_id="8" servo_inverted="True"/>
    </ros2_control>
  </xacro:macro>
</robot>
