<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

    <ros2_control name="GazeboSystem" type="system">
        <!-- <hardware> Fix in https://github.com/Dr-QP/Dr.QP/issues/91
            <plugin>gazebo_ros2_control/GazeboSystem</plugin>
        </hardware> -->
        <xacro:macro name="hexjoint" params="name">
            <joint name="${name}">
                <command_interface name="position">
                    <param name="min">-15</param>
                    <param name="max">15</param>
                </command_interface>
                <state_interface name="position">
                    <param name="initial_value">1.0</param>
                </state_interface>
                <state_interface name="velocity" />
                <state_interface name="effort" />
            </joint>
        </xacro:macro>

        <xacro:hexjoint name="left_back_coxa" />
        <xacro:hexjoint name="left_back_femur" />
        <xacro:hexjoint name="left_back_tibia" />
        <!-- <xacro:hexjoint name="left_middle_coxa" /> -->
        <!-- <xacro:hexjoint name="left_middle_femur" /> -->
        <!-- <xacro:hexjoint name="left_middle_tibia" /> -->
        <!-- <xacro:hexjoint name="left_front_coxa" /> -->
        <!-- <xacro:hexjoint name="left_front_femur" /> -->
        <!-- <xacro:hexjoint name="left_front_tibia" /> -->
        <!-- <xacro:hexjoint name="right_front_coxa" /> -->
        <!-- <xacro:hexjoint name="right_front_femur" /> -->
        <!-- <xacro:hexjoint name="right_front_tibia" /> -->
        <!-- <xacro:hexjoint name="right_middle_coxa" /> -->
        <!-- <xacro:hexjoint name="right_middle_femur" /> -->
        <!-- <xacro:hexjoint name="right_middle_tibia" /> -->
        <!-- <xacro:hexjoint name="right_back_coxa" /> -->
        <!-- <xacro:hexjoint name="right_back_femur" /> -->
        <!-- <xacro:hexjoint name="right_back_tibia" /> -->
    </ros2_control>

    <!-- <gazebo> Fix in https://github.com/Dr-QP/Dr.QP/issues/91
        <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
          <robot_param>robot_description</robot_param>
          <robot_param_node>robot_state_publisher</robot_param_node>
          <parameters>$(find drqp_gazebo)/config/gazebo_params.yaml</parameters>
        </plugin>
    </gazebo> -->
</robot>
