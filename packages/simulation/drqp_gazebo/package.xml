<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>drqp_gazebo</name>
  <version>0.1.0</version>
  <description>Dr.QP robot URDF</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <depend>ros2_controllers</depend>

  <exec_depend>ros2launch</exec_depend>
  <exec_depend>ros_gz_sim</exec_depend>
  <exec_depend>gz_ros2_control</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>drqp_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <!-- <gazebo_ros gazebo_model_path="${prefix}/.."/> This adds package path GAZEBO_MODEL_PATH which is required to load meshes with package_to_model conversion -->
    <!-- <gazebo_ros gazebo_media_path="${prefix}/models"/>  This will override default resource path as GAZEBO_RESOURCE_PATH and break gazebo loading. Figure out why defautl is not preserved -->
    <!-- <gazebo_ros plugin_path="${prefix}/plugin"/> -->

    <!-- model, plugin, media = GazeboRosPaths.get_paths()
    if 'GAZEBO_MODEL_PATH' in environ:
        model += pathsep+environ['GAZEBO_MODEL_PATH']
    if 'GAZEBO_PLUGIN_PATH' in environ:
        plugin += pathsep+environ['GAZEBO_PLUGIN_PATH']
    if 'GAZEBO_RESOURCE_PATH' in environ:
        media += pathsep+environ['GAZEBO_RESOURCE_PATH']

    env = {
        'GAZEBO_MODEL_PATH': model,
        'GAZEBO_PLUGIN_PATH': plugin,
        'GAZEBO_RESOURCE_PATH': media,
    } -->
  </export>
</package>
