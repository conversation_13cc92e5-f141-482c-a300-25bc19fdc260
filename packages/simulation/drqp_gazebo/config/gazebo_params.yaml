gazebo:
  ros__parameters:
    publish_rate: 400.0

# This should go to drqt_control package probably
controller_manager:
  ros__parameters:
    # update_rate: 1000  # Hz - gazebo control complains if it is lower 1KHz
    use_sim_time: true

    update_rate: 60  # Hz - for real hardware
    # diagnostic_period: 1

    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

    position_trajectory_controller:
      type: joint_trajectory_controller/JointTrajectoryController
    # effort_controller:
    #   type: effort_controllers/JointGroupEffortController

position_trajectory_controller:
  ros__parameters:
    joints:
      - left_back_coxa
      - left_back_femur
      - left_back_tibia
      # - left_middle_coxa
      # - left_middle_femur
      # - left_middle_tibia
      # - left_front_coxa
      # - left_front_femur
      # - left_front_tibia
      # - right_front_coxa
      # - right_front_femur
      # - right_front_tibia
      # - right_middle_coxa
      # - right_middle_femur
      # - right_middle_tibia
      # - right_back_coxa
      # - right_back_femur
      # - right_back_tibia
    
    interface_name: position
    command_interfaces:
      - position
    state_interfaces:
      - position
      - velocity
    # gains:
    #   p: 100
    #   i: 0
    #   d: 5
    # interpolation_method: splines


# effort_controller:
#   ros__parameters:
#     joints:
#       - left_back_coxa
#       - left_back_femur
#       - left_back_tibia
#       # - left_middle_coxa
#       # - left_middle_femur
#       # - left_middle_tibia
#       # - left_front_coxa
#       # - left_front_femur
#       # - left_front_tibia
#       # - right_front_coxa
#       # - right_front_femur
#       # - right_front_tibia
#       # - right_middle_coxa
#       # - right_middle_femur
#       # - right_middle_tibia
#       # - right_back_coxa
#       # - right_back_femur
#       # - right_back_tibia
#     interface_name: effort
#     command_interfaces:
#       - position
#     state_interfaces:
#       - effort
#       - position

# This should not be needed
# joint_state_broadcaster:
#   ros__parameters:
#     extra_joints:
#       - base_bottom
#       - base_bottom_to_camera
#       - left_back_coxa_servo
#       - left_back_coxa_inner
#       - left_back_femur_inner_1
#       - left_back_femur_inner_2
#       - left_back_tibia_leg
#       - left_back_foot

#       - left_middle_coxa_servo
#       - left_middle_coxa_inner
#       - left_middle_femur_inner_1
#       - left_middle_femur_inner_2
#       - left_middle_tibia_leg
#       - left_middle_foot

#       - left_front_coxa_servo
#       - left_front_coxa_inner
#       - left_front_femur_inner_1
#       - left_front_femur_inner_2
#       - left_front_tibia_leg
#       - left_front_foot

#       - right_front_coxa_servo
#       - right_front_coxa_inner
#       - right_front_femur_inner_1
#       - right_front_femur_inner_2
#       - right_front_tibia_leg
#       - right_front_foot

#       - right_middle_coxa_servo
#       - right_middle_coxa_inner
#       - right_middle_femur_inner_1
#       - right_middle_femur_inner_2
#       - right_middle_tibia_leg
#       - right_middle_foot

#       - right_back_coxa_servo
#       - right_back_coxa_inner
#       - right_back_femur_inner_1
#       - right_back_femur_inner_2
#       - right_back_tibia_leg
#       - right_back_foot



