# Dr.QP

[![ci](https://github.com/Dr-QP/Dr.QP/actions/workflows/ci.yml/badge.svg)](https://github.com/Dr-QP/Dr.QP/actions/workflows/ci.yml?query=branch%3Amain)
[![CodeQL Advanced](https://github.com/Dr-QP/Dr.QP/actions/workflows/codeql.yml/badge.svg?branch=main)](https://github.com/Dr-QP/Dr.QP/actions/workflows/codeql.yml?query=branch%3Amain)
[![Devcontainer CI](https://github.com/Dr-QP/Dr.QP/actions/workflows/devcontainer.yml/badge.svg?branch=main)](https://github.com/Dr-QP/Dr.QP/actions/workflows/devcontainer.yml?query=branch%3Amain)
[![Build act ubuntu docker images](https://github.com/Dr-QP/Dr.QP/actions/workflows/act-docker-build-image.yml/badge.svg?branch=main)](https://github.com/Dr-QP/Dr.QP/actions/workflows/act-docker-build-image.yml?query=branch%3Amain)

[![codecov](https://codecov.io/gh/Dr-QP/Dr.QP/branch/main/graph/badge.svg?token=MSNH7AK8XX)](https://app.codecov.io/gh/Dr-QP/Dr.QP/tree/main)

Welcome to the Dr.QP project.

[<img width="1295" alt="image" src="https://github.com/user-attachments/assets/95200255-e44b-45f5-b7cc-242add9f426b" />](https://drqp.readthedocs.io/en/latest/Dev/designs.html)

## Docs

All the project information and notes is available on the [Project Docs site](https://drqp.readthedocs.io/en/latest/)

## Project planning

The dev backlog and work-in-progress can be found on the [Project Tab](https://github.com/orgs/Dr-QP/projects/3)

## Dev machine setup

See [installation docs](https://drqp.readthedocs.io/en/latest/installation)
