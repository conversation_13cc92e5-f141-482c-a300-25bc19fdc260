name: Docker metadata action
description: Extract metadata (tags, labels) for docker image
inputs:
  images:
    description: Image names to pass to metadata-action
    default: ''
  arch:
    description: Architecture to use as suffix
    required: false
    default: ''
  prefix:
    description: A tag prefix
    required: false
    default: ''
outputs:
  tags:
    description: Docker tags for the images
    value: ${{ steps.meta.outputs.tags }}
  tag:
    description: Docker tag for the images
    value: ${{ steps.single-tag.outputs.result }}
  labels:
    description: Docker labels for the images
    value: ${{ steps.meta.outputs.labels }}
  version:
    description: Docker version for the images
    value: ${{ steps.meta.outputs.version }}
  json:
    description: Docker json for the images
    value: ${{ steps.meta.outputs.json }}
runs:
  using: composite
  steps:
    - name: Generate suffix and prefix if set
      shell: bash
      id: extra-args
      run: |
        EXTRA_ARGS=''
        if [ -n "${{ inputs.prefix }}" ]; then
          EXTRA_ARGS="${EXTRA_ARGS},prefix=${{ inputs.prefix }}"
        fi
        if [ -n "${{ inputs.arch }}" ]; then
          EXTRA_ARGS="${EXTRA_ARGS},suffix=-${{ inputs.arch }}"
        fi
        echo "result=${EXTRA_ARGS}" >> "$GITHUB_OUTPUT"


    - uses: docker/metadata-action@v5
      id: meta
      with:
        images: ${{ inputs.images }}
        tags: |
          type=ref,event=branch${{ steps.extra-args.outputs.result }}
          type=ref,event=pr${{ steps.extra-args.outputs.result }}
          type=semver,pattern={{version}}${{ steps.extra-args.outputs.result }}
          type=semver,pattern={{major}}.{{minor}}${{ steps.extra-args.outputs.result }}
          type=edge,branch=main${{ steps.extra-args.outputs.result }}

    - name: Extract single tag
      shell: bash
      id: single-tag
      run: |
        first_tag=$(echo "${{ steps.meta.outputs.tags }}" | head -n1 | tr -d '\r')
        echo "result=$first_tag" >> "$GITHUB_OUTPUT"

