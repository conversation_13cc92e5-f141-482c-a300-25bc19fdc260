name: <PERSON>er merge multiarch
description: Merge and push multiarch docker images. Based on https://docs.docker.com/build/ci/github-actions/multi-platform/#distribute-build-across-multiple-runners
inputs:
  image:
    description: Image names to pass to metadata-action
    required: true
  future-tag:
    description: A tag that will be assigned in the future. Used to distinguish digests for the merge
    required: false
    default: ''
  registry:
    description: Docker registry to push to
    required: true
  registry-username:
    description: Docker registry username
    required: true
  registry-token:
    description: GitHub token
    required: true
outputs:
  image:
    description: Docker tag for the images
    value: ${{ steps.meta.outputs.tag }}
runs:
  using: composite
  steps:
    - name: Prepare digest artifact name
      shell: bash
      id: digests
      env:
        ARTIFACT_NAME: digests-${{ inputs.image }}${{ inputs.future-tag }}-
        DIGESTS_DIR: ${{ runner.temp }}/digests/${{ inputs.image }}-${{ inputs.future-tag }}
      run: |
        echo "artifact_name_prefix=${ARTIFACT_NAME//\//-}" >> "$GITHUB_OUTPUT"

        mkdir -p "${DIGESTS_DIR}"
        DIGESTS_DIR=$(mktemp -d "${DIGESTS_DIR}/XXXXXXXXX")
        echo "digests_dir=${DIGESTS_DIR}" >> "$GITHUB_OUTPUT"

    - name: Download digests
      uses: actions/download-artifact@v4
      with:
        path: ${{ steps.digests.outputs.digests_dir }}
        pattern: ${{ steps.digests.outputs.artifact_name_prefix }}*
        merge-multiple: true

    - uses: docker/setup-buildx-action@v3
    - uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ inputs.registry-username }}
        password: ${{ inputs.registry-token }}

    - name: Extract metadata (tags, labels) for docker image
      id: meta
      uses: ./.github/actions/docker/metadata-action
      with:
        images: ${{ inputs.image }}
        prefix: ${{ inputs.future-tag }}

    - name: Create manifest list and push
      shell: bash
      working-directory: ${{ steps.digests.outputs.digests_dir }}
      run: |
        docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
          $(printf '${{ inputs.image }}@sha256:%s ' *)

    - name: Inspect image
      shell: bash
      run: |
        docker buildx imagetools inspect ${{ inputs.image }}:${{ steps.meta.outputs.version }}

    - name: Cleanup digests temp dir
      if: ${{ inputs.export-digest == 'true' }}
      shell: bash
      env:
        DIGESTS_DIR: ${{ steps.digests.outputs.digests_dir }}
      run: |
        rm -rf "${DIGESTS_DIR}/*"
