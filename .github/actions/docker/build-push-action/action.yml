name: Docker build and push action
description: Build and push docker image
inputs:
  context:
    description: Context for Dockerfile build
    required: true
  file:
    description: Dockerfile to use
    required: false
  image:
    description: Image names to pass to metadata-action
    required: true
  future-tag:
    description: A tag that will be assigned in the future. Used to distinguish digests for the merge
    required: false
    default: ''
  arch:
    description: Architecture to use as suffix
    required: true
  registry:
    description: Docker registry to push to
    required: true
  registry-username:
    description: Docker registry username
    required: true
  registry-token:
    description: GitHub token
    required: true
  build-args:
    description: Build arguments for Dockerfile
    required: false
  export-digest:
    description: Export digest as artifacts
    required: false
    default: 'true'
outputs:
  digest:
    description: Digest of the built image
    value: ${{ steps.build.outputs.digest }}
runs:
  using: composite
  steps:
    - uses: docker/setup-buildx-action@v3
    - uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ inputs.registry-username }}
        password: ${{ inputs.registry-token }}

    - name: Extract metadata (labels) for docker image
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ${{ inputs.image }}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v6
      env:
        REGISTRY_CACHE: type=registry,ref=${{ inputs.image }}:cache-${{ inputs.future-tag }}${{ inputs.arch }}
        GHA_CACHE: type=gha,scope=${{ inputs.image }}-${{ inputs.future-tag }}${{ inputs.arch }}
      with:
        context: ${{ inputs.context }}
        file: ${{ inputs.file }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: ${{ env.ACT != '' && env.REGISTRY_CACHE || env.GHA_CACHE }}
        cache-to: ${{ env.ACT != '' && env.REGISTRY_CACHE || env.GHA_CACHE }},mode=max
        build-args: ${{ inputs.build-args }}
        outputs: type=image,"name=${{ inputs.image }}",push-by-digest=true,name-canonical=true,push=true

    - name: Export digest
      if: ${{ inputs.export-digest == 'true' }}
      shell: bash
      id: digests
      env:
        DIGESTS_DIR: ${{ runner.temp }}/digests/${{ inputs.image }}-${{ inputs.future-tag }}
        DIGEST_CONTENT: ${{ inputs.image }}-${{ inputs.future-tag }}-${{ inputs.arch }}
        DIGEST: ${{ steps.build.outputs.digest }}
        ARTIFACT_NAME: digests-${{ inputs.image }}${{ inputs.future-tag }}-linux-${{ inputs.arch }}
      run: |
        mkdir -p "${DIGESTS_DIR}"
        DIGESTS_DIR=$(mktemp -d "${DIGESTS_DIR}/XXXXXXXXX")
        echo "digests_dir=${DIGESTS_DIR}" >> "$GITHUB_OUTPUT"

        echo "${DIGEST_CONTENT}" > "${DIGESTS_DIR}/${DIGEST#sha256:}"

        echo "artifact_name=${ARTIFACT_NAME//\//-}" >> "$GITHUB_OUTPUT"

    - name: Upload digest
      if: ${{ inputs.export-digest == 'true' }}
      uses: actions/upload-artifact@v4
      with:
        name: ${{ steps.digests.outputs.artifact_name }}
        path: ${{ steps.digests.outputs.digests_dir }}/*
        if-no-files-found: error
        retention-days: 1

    - name: Cleanup digests
      if: ${{ inputs.export-digest == 'true' }}
      shell: bash
      env:
        DIGESTS_DIR: ${{ steps.digests.outputs.digests_dir }}
      run: |
        rm -rf "${DIGESTS_DIR}/*"
