name: Shared paths filters
description: A set of filters to be used to fitler jobs
inputs:
  base-branch:
    description: Base branch
    required: false
    default: main
  extra-filter:
    description: Extra filters
    required: false
    default: 'default-path-that-will-never-match'
outputs:
  pass:
    description: Filtered file present
    value: ${{ steps.filter.outputs.ros }}
runs:
  using: composite
  steps:
  - name: Filter
    id: filter
    uses: dorny/paths-filter@v3.0.2
    with:
      base: ${{ inputs.base-branch }}
      filters: |
        ros:
          - .clang-format
          - .devcontainer/**
          - .github/actions/**
          - codecov.yml
          - docker/**
          - packages/**
          - scripts/**
          - ${{ inputs.extra-filter }}
