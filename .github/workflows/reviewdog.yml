name: reviewdog

on:
  push:
    branches:
      - main
    tags:
      - v*
  pull_request:
    branches:
      - main
  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  actionlint:
    timeout-minutes: 10
    permissions:
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: reviewdog/action-actionlint@v1
        with:
          reporter: github-pr-review # can use Markdown and add a link to rule page in reviewdog reports.
          fail_level: warning
