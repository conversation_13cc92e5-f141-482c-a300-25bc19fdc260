# https://docs.github.com/en/code-security/code-scanning/creating-an-advanced-setup-for-code-scanning/customizing-your-advanced-setup-for-code-scanning#using-a-custom-configuration-file
name: "CodeQL Configuration"
disable-default-queries: true

packs:
  - codeql/javascript-queries:AlertSuppression.ql
  - codeql/python-queries:AlertSuppression.ql
  - codeql/cpp-queries:AlertSuppression.ql

queries:
  - uses: security-extended
  - uses: security-and-quality

query-filters:
  - exclude:
      id: actions/unpinned-tag

# Note: C/C++ does not support path-based filtering when using the manual build mode. The "paths" and "paths-ignore" configuration properties will have no effect for this language. If desired, you can use the advanced-security/filter-sarif Action to rewrite the SARIF file to exclude alerts from these paths. For more information, see https://github.com/advanced-security/filter-sarif **
paths-ignore:
  - "**/node_modules/**"
