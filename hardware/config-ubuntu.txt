[all]
kernel=vmlinuz
cmdline=cmdline.txt
initramfs initrd.img followkernel

[pi4]
max_framebuffers=2
arm_boost=1

[all]
# Enable the audio output, I2C and SPI interfaces on the GPIO header. As these
# parameters related to the base device-tree they must appear *before* any
# other dtoverlay= specification
dtparam=audio=on
dtparam=i2c_arm=on
dtparam=spi=on

# Comment out the following line if the edges of the desktop appear outside
# the edges of your display
disable_overscan=1

# If you have issues with audio, you may try uncommenting the following line
# which forces the HDMI output into HDMI mode instead of DVI (which doesn't
# support audio output)
#hdmi_drive=2

# Enable the serial pins
enable_uart=1

# Autoload overlays for any recognized cameras or displays that are attached
# to the CSI/DSI ports. Please note this is for libcamera support, *not* for
# the legacy camera stack
camera_auto_detect=1
display_auto_detect=1

# Config settings specific to arm64
arm_64bit=1
dtoverlay=dwc2

[cm4]
# Enable the USB2 outputs on the IO board (assuming your CM4 is plugged into
# such a board)
# dtoverlay=dwc2,dr_mode=host


[pi4]
# Dr.QP: # Enable DRM VC4 V3D driver on top of the dispmanx display stack
# dtoverlay=vc4-fkms-v3d
dtoverlay=vc4-kms-v3d


[all]
# Dr.QP: for VC4 V3D driver
# start_x=1
gpu_mem=256

[all]
# Dr.QP: UART via I2C devices. addr is different according to status of A0/A1, default 0X48
dtoverlay=sc16is752-i2c,int_pin=24,addr=0x48

# Skip power supply check on RPi5
usb_max_current_enable=1
