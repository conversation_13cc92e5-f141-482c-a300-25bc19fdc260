{
  "name": "Dr.QP ROS 2 jazzy",
  "image": "ghcr.io/dr-qp/jazzy-ros-desktop:edge",
  "customizations": {
    "vscode": {
      "extensions": [
        // Open the /opt/ros/overlay_ws/Dr.QP.code-workspace to install the recommended extensions
      ]
    }
  },
  "workspaceFolder": "/opt/ros/overlay_ws/",
  "workspaceMount": "source=${localWorkspaceFolder},target=/opt/ros/overlay_ws/,type=bind",
  "containerEnv": {},
  "mounts": [
  ],
  // Second venv command with --system-site-packages turns on access to system packages to allow ROS2 builds to work
  "postCreateCommand": "sudo chown -R root:root /opt/ros/overlay_ws/.ansible /opt/ros/overlay_ws/.vscode /opt/ros/overlay_ws/.cache /opt/ros/overlay_ws/build /opt/ros/overlay_ws/install /opt/ros/overlay_ws/lcov /opt/ros/overlay_ws/log /opt/ros/overlay_ws/docs/_build /opt/ros/overlay_ws/.micromamba /opt/ros/overlay_ws/.venv",
  "postAttachCommand": {
    "git config": "git config --global gpg.ssh.program ssh-keygen",
    "python-venv": "python3 -m venv /opt/ros/overlay_ws/.venv && /opt/ros/overlay_ws/.venv/bin/python3 -m pip install -r /opt/ros/overlay_ws/requirements.txt --use-pep517" // Keep in sync with the task in Dr.QP.code-workspace
  },
  "runArgs": [
    // Commented out to allow Codespaces use
    "--network", "host",
    "--ipc", "host",
    "--pid", "host",
    "--privileged",

    // Uncomment to allow shared memory based DDS communication
    // "--user", "1001:1001", // Change to match primary user on the host

    "--mount", "type=volume,source=drqp-ansible,target=/opt/ros/overlay_ws/.ansible",
    "--mount", "type=volume,source=drqp-vscode,target=/opt/ros/overlay_ws/.vscode",
    "--mount", "type=volume,source=drqp-clangd-cache,target=/opt/ros/overlay_ws/.cache",
    "--mount", "type=volume,source=drqp-colcon-build,target=/opt/ros/overlay_ws/build",
    "--mount", "type=volume,source=drqp-colcon-install,target=/opt/ros/overlay_ws/install",
    "--mount", "type=volume,source=drqp-colcon-lcov,target=/opt/ros/overlay_ws/lcov",
    "--mount", "type=volume,source=drqp-colcon-log,target=/opt/ros/overlay_ws/log",
    "--mount", "type=volume,source=drqp-docs-build,target=/opt/ros/overlay_ws/docs/_build",
    "--mount", "type=volume,source=drqp-python-micromamba,target=/opt/ros/overlay_ws/.micromamba",
    "--mount", "type=volume,source=drqp-python-venv,target=/opt/ros/overlay_ws/.venv",
  ],
  "features": {
  }
}
