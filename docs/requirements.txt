# Requirements to build the Python documentation
#
# Note that when updating this file, you will likely also have to update
# the docs/constraints.txt file.

# The Sphinx version is pinned so that new versions that introduce new warnings
# won't suddenly cause build failures. Updating the version is fine as long
# as no warnings are raised by doing so.
# Keep this version in sync with ``docs/conf.py``.
sphinx~=8.2.0

sphinx-notfound-page~=1.0.0

# Sphinx theme https://pydata-sphinx-theme.readthedocs.io/en/stable/user_guide/install.html
# 0.16.1 has issue with source link template https://github.com/pydata/pydata-sphinx-theme/issues/2088
pydata-sphinx-theme==0.16.0
sphinx-sitemap>=2.6.0

myst-nb>=1.2.0
myst-parser>=4.0.0
PyGithub>=2.6

sphinx-copybutton
sphinx-design
sphinx-togglebutton
sphinxext-rediraffe # Redirect from deleted files https://github.com/sphinx-doc/sphinxext-rediraffe
sphinxcontrib-lightbox2

-c constraints.txt

# The requirements for notebooks
-r source/notebooks/requirements.txt
