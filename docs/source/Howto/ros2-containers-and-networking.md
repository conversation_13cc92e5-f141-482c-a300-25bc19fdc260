# ROS2 containers and networking

ROS2 employs RTPS which relies on the multicast networking

1. [ROS 2 and Kubernetes
   Basics](https://ubuntu.com/blog/exploring-ros-2-with-kubernetes)
2. [ROS 2 on Kubernetes: a simple talker and listener
   setup](https://ubuntu.com/blog/ros-2-on-kubernetes-a-simple-talker-and-listener-setup)
3. [Distribute ROS 2 across machines with
   MicroK8s](https://ubuntu.com/blog/distribute-ros-2-across-machines-with-kubernetes)
4. [Exploring ROS 2 Kubernetes
   configurations](https://ubuntu.com/blog/exploring-ros-2-kubernetes-configurations)

More of the related stuff

- Discussion: [ROS 2 on Kubernetes](https://discourse.ros.org/t/ros-2-on-kubernetes/17182)
- ROS2 Tutorial [Deploying on IBM Cloud Kubernetes (community-contributed)](https://docs.ros.org/en/humble/Tutorials/Miscellaneous/Deploying-ROS-2-on-IBM-Cloud.html#deploying-on-ibm-cloud-kubernetes-community-contributed)
- [Robotics Distributed System based on Kubernetes](https://discourse.ros.org/t/robotics-distributed-system-based-on-kubernetes/12558)
- [Kubernetes Robotics Distributed System Deep Dive](https://www.slideshare.net/slideshow/kerbernetes-robotics-distributed-system-deep-dive/232498065)
- [FastDDS - New Discovery Server](https://discourse.ros.org/t/new-discovery-server/17383)
- [96boards: Autoware everywhere | Running Cyclone DDS on Kubernetes](https://www.96boards.org/blog/cyclonedds_on_kubernetes/)

Reference:

- [Container networking overview](https://docs.docker.com/engine/network/)
- [containernetworking/cni CNI - the Container Network Interface](https://github.com/containernetworking/cni)
- [Kubernetes Documentation/Getting started](https://kubernetes.io/docs/setup/)

Network troubleshooting:

- [Nmap Cheat Sheet 2025: All the Commands & Flags](https://www.stationx.net/nmap-cheat-sheet/)
- [netshoot: a Docker + Kubernetes network trouble-shooting swiss-army container](https://github.com/nicolaka/netshoot)
