# Notebooks

This directory contains <PERSON><PERSON>ter notebooks for various purposes, including documentation, tutorials, and experiments.

Notebooks are stored in the myst format, which is a markdown-based format that is source control friendly and doesn't contain notebook outputs.

## Running Notebooks

1. Clone the repository and open the workspace as usual.
2. Run `Dr.QP venv` task from VSCode
3. Run `Dr.QP sync notebooks` task from VSCode
