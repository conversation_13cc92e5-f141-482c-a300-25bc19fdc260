# Custom images that include moby-engine (dockerd for docker in docker to work)
# https://github.com/Dr-QP/act_docker_images/pkgs/container/act-ubuntu/369165992?tag=act-24.04-main
-P ubuntu-24.04=ghcr.io/dr-qp/act-ubuntu:act-24.04-edge@sha256:d5f5f251ce5e87196b179323e7fe7ab3f14fdacd47dd667ab2afc3e9605b9ee6
-P ubuntu-24.04-arm=ghcr.io/dr-qp/act-ubuntu:act-24.04-edge@sha256:8f71f401ea8701b79fb5e07f730b85e215abdbc7ccb2e3ed1d85b6c79b8d31e6

# In order to run arm64 images on amd64 linux QEMU needs to be installed by running:
# docker run --privileged --rm tonistiigi/binfmt --install arm64

# A required option for artifacts server used in docker image builds to store digests
--artifact-server-path /tmp/act-artifacts

# privileged is needed for Docker in Docker to work in devcontainer builds
# Move this option to command line as it is not supported here at the moment
# --container-options "--privileged"

# Other needed command line options
# --actor <your-github-user>
# Configure secrets.GITHUB_TOKEN with your github PAT with packages:write access
