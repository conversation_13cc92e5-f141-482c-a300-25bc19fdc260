{
  "folders": [
    {
      "path": ".",
      "name": "<PERSON><PERSON><PERSON><PERSON>"
    },
  ],
  "extensions": {
    // cSpell:disable
    "recommendations": [
      "chrisdias.vscode-opennewinstance",
      "dlech.chmod",
      "eamodio.gitlens",
      "hangxingliu.vscode-systemd-support",
      "jithurjacob.nbpreviewer",
      "llvm-vs-code-extensions.vscode-clangd",
      "mhutchie.git-graph",
      "ms-python.debugpy",
      "ms-python.python",
      "ms-python.vscode-pylance",
      "ms-vscode-remote.remote-containers",
      "ms-vscode-remote.remote-ssh-edit",
      "ms-vscode-remote.remote-ssh",
      "ms-vscode.cmake-tools",
      "ms-vscode.cpptools-themes",
      "ms-vscode.cpptools",  // MS C++ extension is needed for ROS extension to activate
      "plorefice.devicetree",
      "redhat.vscode-xml",
      "redhat.vscode-yaml",
      "rpinski.shebang-snippets",
      "yy0931.save-as-root",
      "althack.ament-task-provider",
      "nonanonno.vscode-ros2",
      "jaehyunshim.vscode-ros2",
      "vadimcn.vscode-lldb",
      "editorconfig.editorconfig",
      "sanjulaganepola.github-local-actions",
      "streetsidesoftware.code-spell-checker",
      "iliazeus.vscode-ansi",
      "ms-azuretools.vscode-containers",
      "1password.op-vscode",
      "rogalmic.bash-debug",
      "mads-hartmann.bash-ide-vscode",
      "anjali.clipboard-history",
      "ryanluker.vscode-coverage-gutters",
      "github.vscode-github-actions",
      "slevesque.vscode-3dviewer",
      "corker.vscode-micromamba",
      "github.vscode-pull-request-github",
      "augment.vscode-augment",
      "charliermarsh.ruff",
      "tamasfe.even-better-toml",
      "davidanson.vscode-markdownlint",
      "elagil.pre-commit-helper",
      "ms-toolsai.jupyter",
      "ms-toolsai.vscode-jupyter-cell-tags",
      "dotjoshjohnson.xml",
      "batyan-soft.fast-tasks",
      "executablebookproject.myst-highlight",
      "gbraad.systemd-universal-manager",
      "ipierre1.ansible-vault-vscode",
      "redhat.ansible",
      "davidkol.fastcompare",
      "samuelcolvin.jinjahtml",
      "ms-azuretools.vscode-containers",
      "Ranch-Hand-Robotics.rde-ros-2",
      "Ranch-Hand-Robotics.urdf-editor"
    ]
    // cSpell:enable
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "type": "lldb",
        "request": "launch",
        "name": "drqp_control control",
        "program": "install/drqp_control/lib/drqp_control/control",
        "args": [
          "up",
          "*************:2022"
        ],
        "cwd": "${workspaceFolder:Dr.QP}/install/drqp_control/lib/drqp_control",
        "preLaunchTask": "Dr.QP colcon build",
      },
      {
        "type": "lldb",
        "request": "launch",
        "name": "drqp_serial_tests",
        "program": "${workspaceFolder:Dr.QP}/build/drqp_serial/test/drqp_serial_tests",
        "args": [
          // "--order", "rand"
        ],
        "cwd": "${workspaceFolder:Dr.QP}/build/drqp_serial/test/",
        "preLaunchTask": "Dr.QP colcon build",
      },
      {
        "type": "lldb",
        "request": "launch",
        "name": "drqp_a1_16_driver_tests",
        "program": "${workspaceFolder:Dr.QP}/build/drqp_a1_16_driver/test/drqp_a1_16_driver_tests",
        "args": [
          // "--use-real-hardware",
          // "--device", "*************:2022",
          "[focus]",
        ],
        "cwd": "${workspaceFolder:Dr.QP}/build/drqp_a1_16_driver/test/",
        "preLaunchTask": "Dr.QP colcon build",
      },
      {
        "type": "lldb",
        "request": "launch",
        "name": "drqp_control pose_reader",
        "program": "${workspaceFolder:Dr.QP}/build/drqp_control/pose_reader",
        "args": [],
        "cwd": "${workspaceFolder:Dr.QP}/build/drqp_control/",
        "preLaunchTask": "Dr.QP colcon build",
      },
      {
        "type": "lldb",
        "request": "attach",
        "name": "ros2_control_node",
        "program": "ros2_control_node",
      },
      {
        "type": "bashdb",
        "request": "launch",
        "name": "Bash-Debug (current file)",
        "cwd": "${workspaceFolder}",
        "program": "${file}",
        "args": []
      },
      {
        "name": "run_hexapod.py",
        "type": "debugpy",
        "request": "launch",
        "program": "${workspaceFolder}/docs/source/notebooks/run_hexapod.py",
        "console": "integratedTerminal",
        "python": "${workspaceFolder}/.venv/bin/python3",
      },
      {
        "name": "ps5_dualsense.py",
        "type": "debugpy",
        "request": "launch",
        "program": "${workspaceFolder}/docs/source/notebooks/ps5_dualsense.py",
        "console": "integratedTerminal",
        "python": "${workspaceFolder}/.venv/bin/python3",
      },
      {
        "name": "ROS run_hexapod.py",
        "type": "debugpy",
        "request": "launch",
        "program": "${workspaceFolder}/packages/runtime/drqp_brain/drqp_brain/run_hexapod_ros.py",
        "console": "integratedTerminal",
        "env": {
          // Force network mode for DDS network https://robotics.stackexchange.com/a/114955/38643
          // "FASTDDS_BUILTIN_TRANSPORTS": "UDPv4"
        },
        "python": "${workspaceFolder}/.venv-prod/bin/python3",
      }
    ]
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [

      {
        "label": "Dr.QP colcon build",
        "type": "ROS2",
        "command": "python3",
        "args": [
          "-m",
          "colcon",
          "build",
          "--symlink-install",
          "--event-handlers",
          "console_cohesion+",
          "--base-paths",
          "${workspaceFolder}",
          // "--packages-up-to",
          // "drqp_serial",
          // "--executor=sequential",
          // "--mixin",
          // "coverage-pytest",

          "--cmake-args",
          " -GNinja",
          " -D CMAKE_BUILD_TYPE=Debug",
          " -D DRQP_ENABLE_COVERAGE=ON",
          " --no-warn-unused-cli",
          // " --debugger",
          // " --debugger-pipe=/tmp/drqp_serial"
        ],
        "problemMatcher": [
          "$colcon-gcc"
        ],
        "group": {
          "kind": "build",
          "isDefault": true
        },
        "options": {
          "env": {
            "CMAKE_EXPORT_COMPILE_COMMANDS": "1",
            "CC": "clang",
            "CXX": "clang++"
          }
        }
      },
      {
        "label": "Dr.QP colcon test",
        "type": "ROS2",
        "command": "python3",
        "args": [
          "-m",
          "colcon",
          "test",
          "--event-handlers",
          "console_cohesion+",
          "summary+",
          "status+",
          "--return-code-on-test-failure",
          "--mixin",
          "coverage-pytest",
          // "--packages-up-to",
          // "drqp_serial",
          // "--packages-select",
          // "drqp_brain",
        ],
        "problemMatcher": [
          "$colcon-gcc"
        ],
        "group": {
          "kind": "test",
          "isDefault": true
        },
      },
      {
        "label": "Dr.QP coverage",
        "type": "shell",
        "command": "${workspaceFolder}/packages/cmake/llvm-cov-export-all.py ${workspaceFolder}",
        "problemMatcher": [],
      },
      {
        "label": "Dr.QP build, test, coverage",
        "type": "shell",
        "command": "echo done",
        "problemMatcher": [],
        "dependsOrder": "sequence",
        "dependsOn": [
          "Dr.QP colcon build",
          "Dr.QP colcon test",
          "Dr.QP coverage"
        ]
      },
      {
        "label": "Dr.QP clean",
        "type": "shell",
        "command": "${workspaceFolder}/scripts/clean.fish",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "problemMatcher": []
      },
      {
        "label": "Dr.QP ament cpplint",
        "type": "ament",
        "task": "cpplint",
        "path": "${workspaceFolder}/packages/",
        "problemMatcher": [
          "$ament_cpplint"
        ],
      },
      {
        "label": "Dr.QP ament lint cmake",
        "type": "ament",
        "task": "lint_cmake",
        "path": "${workspaceFolder}/packages/",
        "problemMatcher": [
          "$ament_lint_cmake"
        ],
      },
      {
        "label": "Dr.QP rosdep",
        "type": "ROS2",
        "command": "./scripts/ros-dep.sh",
        "problemMatcher": []
      },
      {
        "label": "Dr.QP ser2net",
        "type": "shell",
        "command": "ser2net -d -c ${workspaceFolder}/scripts/ser2net/ser2net.yml",
        "problemMatcher": []
      },
      {
        "label": "Dr.QP venv",
        "type": "shell",
        "command": "python3 -m venv ${workspaceFolder}/.venv && ${workspaceFolder}/.venv/bin/python3 -m pip install -r ${workspaceFolder}/requirements.txt --use-pep517", // Keep in sync with the command in .devcontainer/devcontainer.json
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "problemMatcher": [
          "$python"
        ]
      },
      {
        "label": "Dr.QP prod-venv",
        "type": "shell",
        "command": "${workspaceFolder}/docker/ros/deploy/prod-venv.sh build && ${workspaceFolder}/docker/ros/deploy/prod-venv.sh install",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "problemMatcher": [
          "$python"
        ]
      },
      {
        "label": "Dr.QP setup ROS",
        "type": "shell",
        "command": "${workspaceFolder}/.venv/bin/ansible-playbook playbooks/20_ros_setup.yml -i inventories/localhost.yml",
        "options": {
          "cwd": "${workspaceFolder}/docker/ros/ansible",
        },
        "problemMatcher": [
          "$python"
        ]
      },
      {
        "label": "Dr.QP sync notebooks",
        "type": "shell",
        "command": "${workspaceFolder}/scripts/sync-notebooks.sh",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "problemMatcher": [
          "$python"
        ]
      },
      {
        "label": "Dr.QP build docs server",
        "type": "shell",
        "command": "${workspaceFolder}/.venv/bin/sphinx-autobuild -nW --keep-going -b html docs/source/ docs/_build/html --port 8000",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "isBackground": true,
        "dependsOn": [
          "Dr.QP sync notebooks"
        ],
        "problemMatcher": [
          {
            "owner": "sphinx",
            "fileLocation": "absolute",
            "pattern": {
              "regexp": "^(.*):(\\d+):\\s+(\\w*):\\s+(.*)$",
              "file": 1,
              "line": 2,
              "severity": 3,
              "message": 4
            }
          }
        ]
      },
      {
        "label": "Dr.QP check docs",
        "type": "shell",
        "command": ".venv/bin/sphinx-build -nW --keep-going -b rediraffewritediff docs/source/ docs/_build/html",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "dependsOn": [
          "Dr.QP sync notebooks"
        ],
        "problemMatcher": [
          {
            "owner": "sphinx",
            "fileLocation": "absolute",
            "pattern": {
              "regexp": "^(.*):(\\d+):\\s+(\\w*):\\s+(.*)$",
              "file": 1,
              "line": 2,
              "severity": 3,
              "message": 4
            }
          }
        ]
      },
      {
        "label": "Dr.QP workspace extensions",
        "type": "shell",
        "command": "${workspaceFolder}/scripts/workspace-extensions.sh",
        "options": {
          "cwd": "${workspaceFolder}/scripts"
        },
        "problemMatcher": []
      },
      {
        "label": "Dr.QP xmlunit viewer",
        "type": "shell",
        // xunit-viewer supports only a single folder for -r argument, so ignores is the only way to reduce noise
        "command": "npx -y xunit-viewer -r build --server -o build/xunit-index.html -i Test.xml -i coverage.xml -i package.xml --watch",
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "problemMatcher": [],
        "isBackground": true
      }
    ]
  },
  "settings": {
    "cmake.configureOnEdit": false,
    "cmake.configureOnOpen": false,
    "cmake.cmakePath": "cmake",
    "cmake.generator": "Ninja",
    "cmake.exportCompileCommandsFile": true,

    // "ROS2.distro": "jazzy",
    "ROS2.rosSetupScript": "${workspaceFolder}/scripts/setup.bash",
    "ament-task-provider.envSetup": "source /opt/ros/jazzy/setup.bash",

    // Draft for .micromamba ros-jazzy, but still doesn't work
    // "ROS2.rosRootDir": ".micromamba/envs/ros-jazzy/",
    // "ROS2.rosSetupScript": ".micromamba/envs/ros-jazzy/setup.bash",
    // "ament-task-provider.envSetup": "source .micromamba/envs/ros-jazzy/setup.bash",
    "python.autoComplete.extraPaths": [
      "/opt/ros/jazzy/lib/python3.12/site-packages",
      "/opt/ros/jazzy/local/lib/python3.12/dist-packages"
    ],
    "python.analysis.extraPaths": [
      "/opt/ros/jazzy/lib/python3.12/site-packages",
      "/opt/ros/jazzy/local/lib/python3.12/dist-packages"
    ],
    "python.testing.pytestArgs": [
        "packages"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    "[python]": {
      "editor.formatOnSave": true,
      "editor.defaultFormatter": "charliermarsh.ruff" // cSpell:disable-line
    },
    "ruff.organizeImports": true,
    "ruff.fixAll": true,
    "ruff.lint.enable": true,

    "search.exclude": {
      "**/node_modules": true,
      "**/bower_components": true,
      "**/*.code-search": true,
      "/build/**": true,
      "/install/**": true,
      "/log/**": true,
    },
    "clangd.enable": true,
    "clangd.path": "clangd",
    "C_Cpp.intelliSenseEngine": "disabled",
    "[cpp]": {
      "editor.defaultFormatter": "llvm-vs-code-extensions.vscode-clangd"
    },

    "coverage-gutters.coverageBaseDir": "build/**",
    "coverage-gutters.coverageFileNames": [
      "*.info",
    ],
    "coverage-gutters.showLineCoverage": true,
    "coverage-gutters.highlightdark": "rgba(0, 122, 30, 0.1)",
    "coverage-gutters.partialHighlightDark": "rgba(163, 149, 0, 0.1)",
    "coverage-gutters.noHighlightDark": "rgba(163, 0, 0, 0.1)",
    "esbonio.server.enabled": false,

    "cSpell.words": [
      "amsmath",
      "arange",
      "arccos",
      "arctan",
      "arctan2",
      "argsort",
      "autoreload",
      "autosummary",
      "azim",
      "bashdb",
      "bezier",
      "Bézier",
      "bspline",
      "clangd",
      "colab",
      "colcon",
      "cpplint",
      "debugpy",
      "deflist",
      "dollarmath",
      "dr_qp",
      "drqp",
      "EEPROM",
      "esbonio",
      "figsize",
      "fontsize",
      "getpid",
      "highlightdark",
      "hypot",
      "interp",
      "intersphinx",
      "ioff",
      "ipympl",
      "ipynb",
      "jupytext",
      "libstdc",
      "linestyle",
      "linspace",
      "Matosov",
      "matplotlib",
      "micromamba",
      "noqa",
      "numpy",
      "Pololu",
      "pyplot",
      "pytest",
      "rclcpp",
      "rclpy",
      "rgba",
      "rotmatrix",
      "rotvec",
      "splprep",
      "splrep",
      "stylesheet",
      "toctree",
      "venv",
      "xaxis",
      "xaxislabel",
      "xlabel",
      "xlim",
      "xnew",
      "xtick",
      "yaxis",
      "yaxislabel",
      "ylabel",
      "ylim",
      "ynew",
      "ytick",
      "zaxis",
      "zaxislabel",
      "zlabel",
      "zlim",
      "znew",
      "ztick"
    ],
    "ansible.ansible.path": ".venv/bin/ansible",
    "ansible.python.interpreterPath": "${workspaceFolder}/.venv/bin/python3",
    "ansible.validation.lint.path": ".venv/bin/ansible-lint",
    "files.associations": {
      "**/ansible/**/*.yml": "ansible",
      "**/ansible/**/*.yaml": "ansible",
    },
    "python.analysis.typeCheckingMode": "standard",
    "python.defaultInterpreterPath": "/usr/bin/python3",

    "ros2-topic-viewer.rosSetupScript": "/media/anton/dev/ros_workspaces/drqp_ws/src/Dr.QP/install/setup.bash",
    "ansibleVault.encryptVaultId": "",
  }
}
