#!/usr/bin/env bash
set -e

script_dir=$(dirname $0)
source "$script_dir/__utils.sh"

if [[ -z $ROS_DISTRO ]]; then
  echo "Set ROS_DISTRO to required distribution or source setup file"
  exit 1
fi

source /opt/ros/"$ROS_DISTRO"/setup.bash

keys=$(rosdep keys --from-paths "$sources_dir")
resolved_list=$(rosdep resolve $keys 2>/dev/null || true)

# Array to store package names
packages=()

# Convert the variable into a line-by-line stream
found_apt=false
while IFS= read -r line; do
    # If the line is "#apt", mark that the next line is a package name
    if [[ "$line" == "#apt" ]]; then
        found_apt=true
        continue
    fi

    # If we previously saw "#apt", this line is the package name
    if $found_apt; then
        packages+=("$line")
        echo "Adding ROS package: $line"
        found_apt=false  # Reset flag
    fi
done <<< "$resolved_list"

# Check if there are packages to install
if [[ ${#packages[@]} -eq 0 ]]; then
    echo "No packages found for installation."
    exit 1
fi

# Sort packages
sorted_packages=($(for element in "${packages[@]}"; do echo "$element"; done | sort))

# Create ansible vars directory
ansible_vars_dir="$root_dir/ansible/roles/ros_dependencies/vars"
mkdir -p "$ansible_vars_dir"

# Generate the variables file
cat <<EOF > "$ansible_vars_dir/known_ros_dependencies.yml"
---
# ROS dependencies - GENERATED FILE
# Generated by ros-dep-gen-ansible.sh on $(date)
# This file contains the known ROS dependencies for the project
# DO NOT EDIT MANUALLY

# List of ROS packages to install
ros_dependencies_known_packages:
$(for pkg in "${sorted_packages[@]}"; do echo "  - \"$pkg\""; done)
EOF

echo "Ansible variables file generated at: $ansible_vars_dir/known_ros_dependencies.yml"
echo "Known ROS dependencies have been updated."
echo "Run the playbook normally to use these dependencies."
