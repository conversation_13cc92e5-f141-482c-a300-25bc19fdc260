---
- name: Bring up virtual bots
  hosts: localhost
  vars:
    # container_image: "ghcr.io/dr-qp/jazzy-ros-deploy:edge"
    # container_image: "ros:jazzy-ros-base"
    container_image: "ubuntu:24.04"
  tasks:
    - name: Pull the base image
      community.docker.docker_image:
        name: "{{ container_image }}"
        source: pull

    - name: Create and start containers
      community.docker.docker_container:
        name: "{{ item }}"
        image: "{{ container_image }}"
        state: started
        detach: true
        command: sleep infinity
      loop: "{{ groups['virtual_bots'] | default([]) }}"

    - name: Install Python and sudo in containers
      community.docker.docker_container_exec:
        container: "{{ item }}"
        command: >
          bash -c "apt-get update && apt-get install -y python3 sudo"
      loop: "{{ groups['virtual_bots'] | default([]) }}"
