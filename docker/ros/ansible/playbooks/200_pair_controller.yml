---
- name: Pair a DualSense Wireless Controller
  hosts: all
  vars:
    # Only one is needed
    # controller_mac: "D0:BC:C1:F4:F9:8B"
    # controller_name: ""

    controller_mac: ""
    controller_name: "DualSense"
  gather_facts: false
  tasks:
    - name: Install Bluetooth stack
      when: ansible_become_pass is defined
      become: true
      ansible.builtin.apt:
        name:
          - bluez
          - expect
        state: present
        update_cache: true

    - name: Create temporary directory
      ansible.builtin.tempfile:
        state: directory
        prefix: bt_pair_
      register: temp_dir

    - name: Copy bt_pair.expect script to remote host
      ansible.builtin.copy:
        src: "{{ playbook_dir }}/../../../../scripts/bt_pair.expect"
        dest: "{{ temp_dir.path }}/bt_pair.expect"
        mode: "0755"

    - name: Pair controller by MAC or name
      register: pairing_result
      changed_when: "'already paired' not in pairing_result.stdout and 'Paired successfully' in pairing_result.stdout"
      ansible.builtin.command:
        cmd: '{{ temp_dir.path }}/bt_pair.expect --mac "{{ controller_mac }}" --name "{{ controller_name }}"'

    - name: Clean up temporary directory
      ansible.builtin.file:
        path: "{{ temp_dir.path }}"
        state: absent
      when: temp_dir.path is defined
