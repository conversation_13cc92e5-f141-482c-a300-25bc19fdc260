---
# ROS dependencies - GENERATED FILE
# Generated by ros-dep-gen-ansible.sh on Thu Apr 24 06:36:24 UTC 2025
# This file contains the known ROS dependencies for the project
# DO NOT EDIT MANUALLY

# List of ROS packages to install
ros_dependencies_known_packages:
  - "libboost-all-dev"
  - "python3-pytest"
  - "ros-jazzy-ament-cmake"
  - "ros-jazzy-ament-cmake-clang-format"
  - "ros-jazzy-ament-cmake-copyright"
  - "ros-jazzy-ament-cmake-core"
  - "ros-jazzy-ament-cmake-cppcheck"
  - "ros-jazzy-ament-cmake-cpplint"
  - "ros-jazzy-ament-cmake-export-dependencies"
  - "ros-jazzy-ament-cmake-flake8"
  - "ros-jazzy-ament-cmake-lint-cmake"
  - "ros-jazzy-ament-cmake-pep257"
  - "ros-jazzy-ament-cmake-xmllint"
  - "ros-jazzy-ament-copyright"
  - "ros-jazzy-ament-flake8"
  - "ros-jazzy-ament-lint-auto"
  - "ros-jazzy-ament-pep257"
  - "ros-jazzy-catch-ros2"
  - "ros-jazzy-gz-ros2-control"
  - "ros-jazzy-joint-state-publisher"
  - "ros-jazzy-joint-state-publisher-gui"
  - "ros-jazzy-joy"
  - "ros-jazzy-launch"
  - "ros-jazzy-launch-ros"
  - "ros-jazzy-rclcpp"
  - "ros-jazzy-rclpy"
  - "ros-jazzy-robot-state-publisher"
  - "ros-jazzy-ros-gz-sim"
  - "ros-jazzy-ros2-control"
  - "ros-jazzy-ros2-controllers"
  - "ros-jazzy-ros2launch"
  - "ros-jazzy-rosidl-default-generators"
  - "ros-jazzy-rosidl-default-runtime"
  - "ros-jazzy-rviz2"
  - "ros-jazzy-sensor-msgs"
  - "ros-jazzy-std-msgs"
  - "ros-jazzy-xacro"
