# ROS Dependencies Role

This Ansible role installs ROS 2 dependencies generated by the `ros-dep-gen-ansible.sh` script, filtering for available packages in the current repositories.

## Example Usage

```yaml
- name: Install ROS dependencies
  hosts: all
  become: true
  roles:
    - { role: ros_dependencies, tags: ["ros_dependencies"] }
```

## Notes

This role uses a pre-generated list of ROS dependencies stored in `vars/known_ros_dependencies.yml`. This file is generated by the `ros-dep-gen-ansible.sh` script, which analyzes the project's ROS dependencies using `rosdep`.

To regenerate the dependencies list, run:

```bash
./scripts/ros-dep-gen-ansible.sh
```

The generated variables (`ros_dependencies_known_packages`) are automatically used by this role.
