---
# ROS dependencies installation

# Always include the variables file (contains known ROS dependencies)
- name: Include known ROS dependencies
  ansible.builtin.include_vars:
    file: "{{ role_path }}/vars/known_ros_dependencies.yml"

- name: Update APT cache
  ansible.builtin.apt:
    update_cache: true

- name: Check policy for all ROS packages at once
  ansible.builtin.command:
    cmd: apt-cache policy --quiet=0 {{ ros_dependencies_known_packages | join(' ') }}
  register: ros_dependencies_pkg_policy
  changed_when: false
  failed_when: false

- name: Extract only the header names (i.e. found packages)
  ansible.builtin.set_fact:
    ros_dependencies_not_available_packages: >-
      {{ ros_dependencies_pkg_policy.stderr_lines
         | select('match', '^.*Unable to locate package.*$')
         | map('regex_replace', 'N: Unable to locate package ','')
         | list }}


- name: Debug
  ansible.builtin.debug:
    msg: "not_available_packages: {{ ros_dependencies_not_available_packages }}"

- name: Remove not available packages
  ansible.builtin.set_fact:
    ros_dependencies_available_packages_list: >-
      {{ ros_dependencies_known_packages
         | difference(ros_dependencies_not_available_packages) }}

- name: Available packages
  ansible.builtin.debug:
    msg: "{{ item }}"
  when: ros_dependencies_available_packages_list | default([]) | length > 0
  loop:
    - "{{ ros_dependencies_available_packages_list }}"

- name: Install available ROS packages
  ansible.builtin.apt:
    name: "{{ ros_dependencies_available_packages_list }}"
    state: present
    install_recommends: false
  when: ros_dependencies_available_packages_list | default([]) | length > 0
