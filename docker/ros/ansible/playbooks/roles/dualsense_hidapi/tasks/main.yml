---
- name: Add udev rules for DualSense controller
  ansible.builtin.copy:
    dest: /etc/udev/rules.d/99-dualsense.rules
    owner: root
    group: root
    mode: "0644"
    content: |
      # ref.: https://boilingsteam.com/the-dualsense-is-making-even-more-sense/
      # copy this file to /etc/udev/rules.d
      # reload udev rules with:
      #   udevadm control --reload-rules
      #   udevadm trigger

      # PS5 DualSense controller over USB hidraw
      KERNEL=="hidraw*", ATTRS{idVendor}=="054c", ATTRS{idProduct}=="0ce6", MODE="0666", TAG+="uaccess"
      # PS5 DualSense Edge controller over USB hidraw
      KERNEL=="hidraw*", ATTRS{idVendor}=="054c", ATTRS{idProduct}=="0df2", MODE="0666", TAG+="uaccess"

      # PS5 DualSense controller over bluetooth hidraw
      KERNEL=="hidraw*", KERNELS=="*054C:0CE6*", MODE="0666", TAG+="uaccess"
      # PS5 DualSense Edge controller over bluetooth hidraw
      KERNEL=="hidraw*", KERNELS=="*054C:0DF2*", MODE="0666", TAG+="uaccess"
  register: dualsense_hidapi_udev_rules_updated
  changed_when: dualsense_hidapi_udev_rules_updated.changed
  notify: Reload udev rules

- name: Install hidapi for DualSense controller support
  ansible.builtin.apt:
    name: libhidapi-dev
    state: present
    update_cache: true
    install_recommends: false
