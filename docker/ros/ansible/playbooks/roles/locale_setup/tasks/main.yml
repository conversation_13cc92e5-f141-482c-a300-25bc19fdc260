---
# UTF-8 locale setup
- name: Check if locale is UTF-8
  ansible.builtin.command: locale
  register: locale_setup_locale_output
  changed_when: false

- name: Set UTF-8 check result
  ansible.builtin.set_fact:
    # Split long jinja expression to avoid line-length violation
    locale_setup_is_utf8: >-
      {{ locale_setup_locale_output.stdout is search('LANG=.*\.UTF-8') or
         locale_setup_locale_output.stdout is search('LC_ALL=.*\.UTF-8') }}

- name: Install locales package
  ansible.builtin.apt:
    name: locales
    state: present
    install_recommends: false
  when: not locale_setup_is_utf8

- name: Generate en_US and en_US.UTF-8 locales
  ansible.builtin.command: locale-gen en_US en_US.UTF-8
  register: locale_setup_locale_gen
  changed_when: locale_setup_locale_gen.stdout is search('done')
  when: not locale_setup_is_utf8

- name: Set locale to en_US.UTF-8
  ansible.builtin.command: update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8
  register: locale_setup_update_locale
  changed_when: locale_setup_update_locale.rc == 0
  when: not locale_setup_is_utf8

- name: Update system profile
  ansible.builtin.copy:
    dest: /etc/profile.d/set-locale.sh
    content: |
      export LANG=en_US.UTF-8
      export LC_ALL=en_US.UTF-8
    owner: root
    group: root
    mode: "0644"

- name: Verify UTF-8 locale is set
  ansible.builtin.command: locale
  register: locale_setup_locale_updated
  changed_when: false

- name: Set UTF-8 check result
  ansible.builtin.set_fact:
    # Split long jinja expression to avoid line-length violation
    locale_setup_is_utf8: >-
      {{ locale_setup_locale_updated.stdout is search('LANG=.*\.UTF-8') or
         locale_setup_locale_updated.stdout is search('LC_ALL=.*\.UTF-8') }}

- name: Fail if UTF-8 locale is not set
  ansible.builtin.fail:
    msg: "UTF-8 locale is not set properly"
  when: not locale_setup_is_utf8
