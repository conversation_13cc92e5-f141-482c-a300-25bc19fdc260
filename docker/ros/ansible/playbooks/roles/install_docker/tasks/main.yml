---
- name: Install Docker dependencies
  ansible.builtin.apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
    state: present
    update_cache: true

- name: Add Docker GPG key
  ansible.builtin.apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present
    keyring: /etc/apt/keyrings/docker.asc

- name: Add Docker repository
  ansible.builtin.apt_repository:
    repo: >-
      deb [arch={{ system_arch }} signed-by=/etc/apt/keyrings/docker.asc]
      https://download.docker.com/linux/ubuntu {{ system_dist }} stable
    state: present
    filename: docker
    update_cache: true

- name: Install Docker Engine
  ansible.builtin.apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
      - docker-buildx-plugin
      - docker-compose-plugin
      - fuse-overlayfs
    state: present
    update_cache: true

- name: Configure Docker daemon
  ansible.builtin.copy:
    dest: /etc/docker/daemon.json
    mode: "0644"
    content: |
      {
        "log-driver": "json-file",
        "log-opts": {
          "max-size": "10m",
          "max-file": "3"
        }
      }
  register: install_docker_docker_daemon_config

- name: Restart Docker service to apply config changes
  ansible.builtin.systemd_service:
    name: docker.service
    state: "{{ 'restarted' if install_docker_docker_daemon_config.changed else 'started' }}"
    enabled: true
    daemon_reload: true

- name: Ensure containerd service is running
  ansible.builtin.systemd_service:
    name: containerd.service
    state: started
    enabled: true

- name: Create docker group
  ansible.builtin.group:
    name: docker
    state: present

- name: Add user to docker group
  ansible.builtin.user:
    name: "{{ ansible_user }}"
    groups: docker
    append: true
