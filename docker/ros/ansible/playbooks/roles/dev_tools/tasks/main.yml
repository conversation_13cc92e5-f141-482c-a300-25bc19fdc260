---
# Development tools installation
- name: Install development tools
  ansible.builtin.apt:
    name:
      - bash-completion
      - build-essential
      - cmake
      - dirmngr
      - git
      - git-lfs
      - gnupg2
      - lcov
      - libasio-dev
      - libbullet-dev
      - libcunit1-dev
      - libtinyxml2-dev
      - ninja-build
      - python3-argcomplete
      - python3-colcon-cmake
      - python3-colcon-common-extensions
      - python3-colcon-coveragepy-result
      - python3-colcon-lcov-result
      - python3-colcon-mixin
      - python3-colcon-output
      - python3-colcon-package-information
      - python3-coverage
      - python3-flake8
      - python3-flake8-blind-except
      - python3-flake8-builtins
      - python3-flake8-class-newline
      - python3-flake8-comprehensions
      - python3-flake8-deprecated
      - python3-flake8-docstrings
      - python3-flake8-import-order
      - python3-flake8-quotes
      - python3-mypy
      - python3-pip
      - python3-pytest
      - python3-pytest-cov
      - python3-pytest-mock
      - python3-pytest-repeat
      - python3-pytest-rerunfailures
      - python3-pytest-runner
      - python3-pytest-timeout
      - python3-rosdep
      - python3-scipy
      - python3-setuptools
      - python3-vcstool
      - python3-venv
      - ros-dev-tools
      - ffmpeg # for rendering notebooks animations
    state: present
    install_recommends: false
