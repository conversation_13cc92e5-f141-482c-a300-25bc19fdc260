---
- name: Remove colcon metadata default
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" metadata remove default
  register: colcon_setup_remove_metadata_result
  changed_when: colcon_setup_remove_metadata_result.rc == 0
  failed_when: colcon_setup_remove_metadata_result.rc != 0 and "A repository with the name 'default' doesn't exist" not in
    colcon_setup_remove_metadata_result.stderr
  ignore_errors: true

- name: Add colcon metadata default
  ansible.builtin.command: >-
    colcon --log-base "{{ colcon_setup_temp_dir.path }}" metadata add default
    https://raw.githubusercontent.com/colcon/colcon-metadata-repository/f0ea17f9a0e70ec0e0e15f869deec2b9f942607d/index.yaml
  register: colcon_setup_add_metadata_result
  changed_when: colcon_setup_add_metadata_result.rc == 0

- name: Update colcon metadata default
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" metadata update default
  register: colcon_setup_update_metadata_result
  changed_when: colcon_setup_update_metadata_result.rc == 0

- name: List colcon metadata
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" metadata list
  register: colcon_setup_list_metadata_result
  changed_when: false
