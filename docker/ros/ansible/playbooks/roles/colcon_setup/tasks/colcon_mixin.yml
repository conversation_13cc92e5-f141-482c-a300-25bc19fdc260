---
- name: Remove default colcon mixin
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" mixin remove default
  register: colcon_setup_remove_result
  changed_when: colcon_setup_remove_result.rc == 0
  failed_when: colcon_setup_remove_result.rc != 0 and "A repository with the name 'default' doesn't exist" not in colcon_setup_remove_result.stderr
  ignore_errors: true

- name: Add default colcon mixin
  ansible.builtin.command: >-
    colcon --log-base "{{ colcon_setup_temp_dir.path }}" mixin add default
    https://raw.githubusercontent.com/colcon/colcon-mixin-repository/b8436aa16c0bdbc01081b12caa253cbf16e0fb82/index.yaml
  register: colcon_setup_add_result
  changed_when: colcon_setup_add_result.rc == 0

- name: Update default colcon mixin
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" mixin update default
  register: colcon_setup_update_result
  changed_when: colcon_setup_update_result.rc == 0

- name: List colcon mixins
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" mixin list
  register: colcon_setup_list_result
  changed_when: false

- name: Show colcon mixins
  ansible.builtin.command: colcon --log-base "{{ colcon_setup_temp_dir.path }}" mixin show
  register: colcon_setup_show_result
  changed_when: false
