---
# Install CMake from system packages
# - set fact
- name: Desired CMake version
  ansible.builtin.set_fact:
    # From https://www.ros.org/reps/rep-2000.html#id50
    cmake_system_min_version: "{{ '3.25.1' if ros_distro == 'jazzy' or ros_distro == 'rolling' else '3.16.3' if ros_distro == 'humble' else 'x.xx.x' }}"
    cmake_system_max_version: "{{ '3.28.3' if ros_distro == 'jazzy' or ros_distro == 'rolling' else '3.22.3' if ros_distro == 'humble' else 'x.xx.x' }}"

- name: Capture Old CMake version
  ansible.builtin.shell:
    cmd: dpkg-query -W -f='${Version}' cmake
    executable: /bin/bash
  register: cmake_system_old_version
  changed_when: false
  failed_when: false

- name: Set IS_KITWARE_CMAKE check result
  ansible.builtin.set_fact:
    cmake_system_is_kitware_cmake: "{{ cmake_system_old_version.stdout is defined and cmake_system_old_version.stdout is search('kitware') }}"

- name: Remove Kitware repository
  ansible.builtin.apt_repository:
    repo: >-
      deb [arch={{ system_arch }} signed-by=/usr/share/keyrings/kitware-archive-keyring.gpg]
      https://apt.kitware.com/ubuntu/ {{ system_dist }} main
    state: absent
    filename: kitware
    update_cache: true

- name: Remove Kitware CMake if present
  ansible.builtin.apt:
    name:
      - cmake
      - cmake-data
    state: absent
  when: cmake_system_is_kitware_cmake

- name: Install system packaged CMake
  ansible.builtin.apt:
    name:
      - cmake
    state: present
    allow_downgrade: true
    clean: true
    update_cache: true
    install_recommends: false

- name: Get installed version of cmake
  ansible.builtin.shell:
    cmd: set -o pipefail && dpkg-query -W -f='${Version}' cmake | grep -oE '[0-9]+\.[0-9]+\.[0-9]+'
    executable: /bin/bash
  register: cmake_system_version
  changed_when: false
  failed_when: false

- name: Fail if cmake version is not in the allowed range
  ansible.builtin.fail:
    msg: >-
      "cmake version must be {{ cmake_system_min_version }} <= and <= {{ cmake_system_max_version }}, "
      "but found {{ cmake_system_version.stdout | default('not installed') }}"
  when: >-
    source_install and
    cmake_system_version.stdout is defined and not (
      cmake_system_version.stdout is version(operator='>=', version=cmake_system_min_version) and
      cmake_system_version.stdout is version(operator='<=', version=cmake_system_max_version)
    )
