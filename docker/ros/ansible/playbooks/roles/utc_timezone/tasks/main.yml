---
# UTC timezone setup tasks

- name: Check current timezone
  ansible.builtin.command: cat /etc/timezone
  register: utc_timezone_current_timezone
  changed_when: false
  check_mode: false

- name: Set timezone to UTC
  ansible.builtin.copy:
    content: "Etc/UTC\n"
    dest: /etc/timezone
    owner: root
    group: root
    mode: "0644"
  register: utc_timezone_timezone_updated
  when: utc_timezone_current_timezone.stdout != "Etc/UTC"

- name: Update localtime link
  ansible.builtin.file:
    src: /usr/share/zoneinfo/Etc/UTC
    dest: /etc/localtime
    state: link
    owner: root
    group: root
    mode: "0644"

- name: Install tzdata package
  ansible.builtin.apt:
    name: tzdata
    state: present
    update_cache: true
    install_recommends: false
