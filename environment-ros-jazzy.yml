##############
name: ros-jazzy

channels:
  - conda-forge
  - robostack-jazzy
  - nodefaults

dependencies:
  - nodejs=20
  - ros-jazzy-desktop
  - compilers
  - cmake
  - pkg-config
  - make
  - ninja
  - colcon-common-extensions
  - catkin_tools
  - rosdep
  - pip:
     - -r ./requirements.txt
     - -r ./docs/requirements.txt
     - esbonio # reStructuredText language server for preview

  # - hidapi # for pydualsense, is not available for arm64 (apple silicon)

  # rosdep pre-install
  - ros-jazzy-joint-state-publisher
  - ros-jazzy-joint-state-publisher-gui
  - ros-jazzy-xacro
  - ros-jazzy-ros2-control
  - ros-jazzy-ros2-controllers
  - ros-jazzy-ament-cmake-clang-format
  - ros-jazzy-ros-gz-sim
  - ros-jazzy-gz-ros2-control
  - libboost-python-devel
  # - ros-jazzy-catch-ros2 # not available
